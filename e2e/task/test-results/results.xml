<testsuites id="" name="" tests="117" failures="0" skipped="117" errors="0" time="0.24997500000000003">
<testsuite name="task-auth.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="chromium" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Task CRUD Authentication › should reject requests without authentication" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should reject requests with invalid JWT" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should reject requests with expired JWT" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should enforce tenant isolation - users cannot access other tenants tasks" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should allow admin users to perform all operations" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should validate user permissions for task operations" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should maintain session context across multiple requests" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should handle concurrent requests from different users" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should validate JWT claims and tenant context" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-create.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="chromium" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Task Creation › should create a basic task successfully" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create a task with priority and due date" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create a task with case assignment" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should handle missing required fields gracefully" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create task with natural language input" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should enforce tenant isolation during creation" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-delete.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="chromium" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Task Deletion › should delete a task successfully" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should delete task with natural language" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle non-existent task deletion gracefully" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should enforce tenant isolation during deletion" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should delete multiple tasks in sequence" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should confirm deletion with task details" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle deletion of completed tasks" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle deletion of high priority tasks" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-read.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="chromium" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Task Reading › should list all tasks for user" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should read tasks with natural language query" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should filter tasks by status" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should filter tasks by priority" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should handle empty task list gracefully" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should enforce tenant isolation when reading" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should read tasks with complex filtering" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-update.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="chromium" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Task Updates › should update task title and description" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task status" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task priority" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task due date" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should complete a task" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task with natural language" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should handle non-existent task update gracefully" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should enforce tenant isolation during updates" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update multiple fields simultaneously" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-auth.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Task CRUD Authentication › should reject requests without authentication" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should reject requests with invalid JWT" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should reject requests with expired JWT" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should enforce tenant isolation - users cannot access other tenants tasks" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should allow admin users to perform all operations" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should validate user permissions for task operations" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should maintain session context across multiple requests" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should handle concurrent requests from different users" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should validate JWT claims and tenant context" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-create.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="firefox" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Task Creation › should create a basic task successfully" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create a task with priority and due date" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create a task with case assignment" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should handle missing required fields gracefully" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create task with natural language input" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should enforce tenant isolation during creation" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-delete.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="firefox" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Task Deletion › should delete a task successfully" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should delete task with natural language" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle non-existent task deletion gracefully" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should enforce tenant isolation during deletion" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should delete multiple tasks in sequence" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should confirm deletion with task details" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle deletion of completed tasks" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle deletion of high priority tasks" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-read.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="firefox" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Task Reading › should list all tasks for user" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should read tasks with natural language query" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should filter tasks by status" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should filter tasks by priority" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should handle empty task list gracefully" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should enforce tenant isolation when reading" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should read tasks with complex filtering" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-update.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Task Updates › should update task title and description" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task status" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task priority" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task due date" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should complete a task" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task with natural language" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should handle non-existent task update gracefully" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should enforce tenant isolation during updates" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update multiple fields simultaneously" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-auth.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Task CRUD Authentication › should reject requests without authentication" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should reject requests with invalid JWT" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should reject requests with expired JWT" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should enforce tenant isolation - users cannot access other tenants tasks" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should allow admin users to perform all operations" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should validate user permissions for task operations" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should maintain session context across multiple requests" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should handle concurrent requests from different users" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task CRUD Authentication › should validate JWT claims and tenant context" classname="task-auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-create.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="webkit" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Task Creation › should create a basic task successfully" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create a task with priority and due date" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create a task with case assignment" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should handle missing required fields gracefully" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should create task with natural language input" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Creation › should enforce tenant isolation during creation" classname="task-create.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-delete.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="webkit" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="Task Deletion › should delete a task successfully" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should delete task with natural language" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle non-existent task deletion gracefully" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should enforce tenant isolation during deletion" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should delete multiple tasks in sequence" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should confirm deletion with task details" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle deletion of completed tasks" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Deletion › should handle deletion of high priority tasks" classname="task-delete.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-read.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="webkit" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Task Reading › should list all tasks for user" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should read tasks with natural language query" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should filter tasks by status" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should filter tasks by priority" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should handle empty task list gracefully" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should enforce tenant isolation when reading" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Reading › should read tasks with complex filtering" classname="task-read.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="task-update.spec.ts" timestamp="2025-05-30T14:26:56.363Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="Task Updates › should update task title and description" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task status" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task priority" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task due date" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should complete a task" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update task with natural language" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should handle non-existent task update gracefully" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should enforce tenant isolation during updates" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Task Updates › should update multiple fields simultaneously" classname="task-update.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>