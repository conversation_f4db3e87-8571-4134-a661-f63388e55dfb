{"config": {"configFile": "/mnt/persist/workspace/e2e/task/playwright.config.ts", "rootDir": "/mnt/persist/workspace/e2e/task/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/mnt/persist/workspace/e2e/task/setup/global-setup.ts", "globalTeardown": "/mnt/persist/workspace/e2e/task/setup/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/mnt/persist/workspace/e2e/task/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/mnt/persist/workspace/e2e/task/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/mnt/persist/workspace/e2e/task/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/mnt/persist/workspace/e2e/task/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/mnt/persist/workspace/e2e/task/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/mnt/persist/workspace/e2e/task/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 2, "webServer": {"command": "python minimal_fastapi_server.py", "url": "http://localhost:8000/health", "reuseExistingServer": true, "timeout": 120000, "env": {"APP_ENV": "test", "SUPABASE_JWT_SECRET": "test-jwt-secret-for-e2e-tests-only", "SUPABASE_URL": "http://localhost:54321", "SUPABASE_ANON_KEY": "test-anon-key", "SUPABASE_SERVICE_ROLE_KEY": "test-service-role-key"}}}, "suites": [{"title": "task-auth.spec.ts", "file": "task-auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Task CRUD Authentication", "file": "task-auth.spec.ts", "line": 21, "column": 6, "specs": [{"title": "should reject requests without authentication", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-7ff200b5b9823e5b7831", "file": "task-auth.spec.ts", "line": 33, "column": 7}, {"title": "should reject requests with invalid JWT", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-3ff0003cd7f0f8eb0161", "file": "task-auth.spec.ts", "line": 58, "column": 7}, {"title": "should reject requests with expired JWT", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-a1e7544ee14b9cb967d2", "file": "task-auth.spec.ts", "line": 83, "column": 7}, {"title": "should enforce tenant isolation - users cannot access other tenants tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-2b491995260d31342d29", "file": "task-auth.spec.ts", "line": 110, "column": 7}, {"title": "should allow admin users to perform all operations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-e84172e009681957ca5f", "file": "task-auth.spec.ts", "line": 144, "column": 7}, {"title": "should validate user permissions for task operations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-98b5ef94b04e6115c5a4", "file": "task-auth.spec.ts", "line": 164, "column": 7}, {"title": "should maintain session context across multiple requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-e23d412ead5db524b2eb", "file": "task-auth.spec.ts", "line": 184, "column": 7}, {"title": "should handle concurrent requests from different users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-9884a5148d4b37376df3", "file": "task-auth.spec.ts", "line": 208, "column": 7}, {"title": "should validate JWT claims and tenant context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-b91e486eeca0663611df", "file": "task-auth.spec.ts", "line": 237, "column": 7}, {"title": "should reject requests without authentication", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-8e1fb40a7b9b34df4bbc", "file": "task-auth.spec.ts", "line": 33, "column": 7}, {"title": "should reject requests with invalid JWT", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-5e69f2f9433b19d4c2fa", "file": "task-auth.spec.ts", "line": 58, "column": 7}, {"title": "should reject requests with expired JWT", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-411ab85f31d159f35973", "file": "task-auth.spec.ts", "line": 83, "column": 7}, {"title": "should enforce tenant isolation - users cannot access other tenants tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-6b6380e8ef923b967c47", "file": "task-auth.spec.ts", "line": 110, "column": 7}, {"title": "should allow admin users to perform all operations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-785fea49e7ef83e8325f", "file": "task-auth.spec.ts", "line": 144, "column": 7}, {"title": "should validate user permissions for task operations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-f2a08ab7cb4877a8cffe", "file": "task-auth.spec.ts", "line": 164, "column": 7}, {"title": "should maintain session context across multiple requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-e0cb81ba2fa8682bd054", "file": "task-auth.spec.ts", "line": 184, "column": 7}, {"title": "should handle concurrent requests from different users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-cacab8dfaefa2508a55c", "file": "task-auth.spec.ts", "line": 208, "column": 7}, {"title": "should validate JWT claims and tenant context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-afc5e221576734e704e9", "file": "task-auth.spec.ts", "line": 237, "column": 7}, {"title": "should reject requests without authentication", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-af8f625315003f800cf8", "file": "task-auth.spec.ts", "line": 33, "column": 7}, {"title": "should reject requests with invalid JWT", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-3177fbc85d21efd9a921", "file": "task-auth.spec.ts", "line": 58, "column": 7}, {"title": "should reject requests with expired JWT", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-57ffc2a1250b8759723d", "file": "task-auth.spec.ts", "line": 83, "column": 7}, {"title": "should enforce tenant isolation - users cannot access other tenants tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-6fe9fd2ab0112be730f3", "file": "task-auth.spec.ts", "line": 110, "column": 7}, {"title": "should allow admin users to perform all operations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-1c2a131c142c41784465", "file": "task-auth.spec.ts", "line": 144, "column": 7}, {"title": "should validate user permissions for task operations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-65c4a0a6b30789a57eea", "file": "task-auth.spec.ts", "line": 164, "column": 7}, {"title": "should maintain session context across multiple requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-7a505d2940e7c2c78c3e", "file": "task-auth.spec.ts", "line": 184, "column": 7}, {"title": "should handle concurrent requests from different users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-144ca359572827789a3a", "file": "task-auth.spec.ts", "line": 208, "column": 7}, {"title": "should validate JWT claims and tenant context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4c00131f4719bd7d184b-d93c9469f938108b9371", "file": "task-auth.spec.ts", "line": 237, "column": 7}]}]}, {"title": "task-create.spec.ts", "file": "task-create.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Task Creation", "file": "task-create.spec.ts", "line": 18, "column": 6, "specs": [{"title": "should create a basic task successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-c0f69acdb935735c9db4", "file": "task-create.spec.ts", "line": 30, "column": 7}, {"title": "should create a task with priority and due date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-fcc1a492e1319c80d04d", "file": "task-create.spec.ts", "line": 56, "column": 7}, {"title": "should create a task with case assignment", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-cbf019aa8b92338b4ac4", "file": "task-create.spec.ts", "line": 85, "column": 7}, {"title": "should handle missing required fields gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-5146bf1dcbd5f3495292", "file": "task-create.spec.ts", "line": 110, "column": 7}, {"title": "should create task with natural language input", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-a507d383428dd86144f9", "file": "task-create.spec.ts", "line": 140, "column": 7}, {"title": "should enforce tenant isolation during creation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-5ccb12f0f62e8cf9248c", "file": "task-create.spec.ts", "line": 161, "column": 7}, {"title": "should create a basic task successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-27122602112e0e67d3d4", "file": "task-create.spec.ts", "line": 30, "column": 7}, {"title": "should create a task with priority and due date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-467f698ae7b23bdb7441", "file": "task-create.spec.ts", "line": 56, "column": 7}, {"title": "should create a task with case assignment", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-f28d143dd0dec5b81217", "file": "task-create.spec.ts", "line": 85, "column": 7}, {"title": "should handle missing required fields gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-70ec801d1615a52ede10", "file": "task-create.spec.ts", "line": 110, "column": 7}, {"title": "should create task with natural language input", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-c686ba083be04465e4cb", "file": "task-create.spec.ts", "line": 140, "column": 7}, {"title": "should enforce tenant isolation during creation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-7d7485bc2b1a1a17c175", "file": "task-create.spec.ts", "line": 161, "column": 7}, {"title": "should create a basic task successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-04b475d20b7fab7f1621", "file": "task-create.spec.ts", "line": 30, "column": 7}, {"title": "should create a task with priority and due date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-2a892cb13e9b54c95fed", "file": "task-create.spec.ts", "line": 56, "column": 7}, {"title": "should create a task with case assignment", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-8e21c3f88d4905936c00", "file": "task-create.spec.ts", "line": 85, "column": 7}, {"title": "should handle missing required fields gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-178695deb25c070ce0de", "file": "task-create.spec.ts", "line": 110, "column": 7}, {"title": "should create task with natural language input", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-29306b6d9ea1214d5f84", "file": "task-create.spec.ts", "line": 140, "column": 7}, {"title": "should enforce tenant isolation during creation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "dc2845ac317b83510066-40251374889bd4620fe5", "file": "task-create.spec.ts", "line": 161, "column": 7}]}]}, {"title": "task-delete.spec.ts", "file": "task-delete.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Task Deletion", "file": "task-delete.spec.ts", "line": 17, "column": 6, "specs": [{"title": "should delete a task successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-9bb63e4ff6ff15c1ae19", "file": "task-delete.spec.ts", "line": 42, "column": 7}, {"title": "should delete task with natural language", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-2534270faa3b10b8e2a6", "file": "task-delete.spec.ts", "line": 64, "column": 7}, {"title": "should handle non-existent task deletion gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-bb70644938b7812f1619", "file": "task-delete.spec.ts", "line": 80, "column": 7}, {"title": "should enforce tenant isolation during deletion", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-c129fff7fae500e14e85", "file": "task-delete.spec.ts", "line": 96, "column": 7}, {"title": "should delete multiple tasks in sequence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-8ad5dd77aa1f22107472", "file": "task-delete.spec.ts", "line": 125, "column": 7}, {"title": "should confirm deletion with task details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-2146d11443575b57d234", "file": "task-delete.spec.ts", "line": 177, "column": 7}, {"title": "should handle deletion of completed tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-2ff48d7c751064dc09d9", "file": "task-delete.spec.ts", "line": 199, "column": 7}, {"title": "should handle deletion of high priority tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-a2061d1e54c5a18eb56a", "file": "task-delete.spec.ts", "line": 225, "column": 7}, {"title": "should delete a task successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-38c3243bad08c2668ae9", "file": "task-delete.spec.ts", "line": 42, "column": 7}, {"title": "should delete task with natural language", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-2ff0ecdae5f52e266ddd", "file": "task-delete.spec.ts", "line": 64, "column": 7}, {"title": "should handle non-existent task deletion gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-4781ed225ef431793af6", "file": "task-delete.spec.ts", "line": 80, "column": 7}, {"title": "should enforce tenant isolation during deletion", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-091ff88d7eb1f597f4b0", "file": "task-delete.spec.ts", "line": 96, "column": 7}, {"title": "should delete multiple tasks in sequence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-8d2bafcab6ed925970e7", "file": "task-delete.spec.ts", "line": 125, "column": 7}, {"title": "should confirm deletion with task details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-c467e5eef421d17017d4", "file": "task-delete.spec.ts", "line": 177, "column": 7}, {"title": "should handle deletion of completed tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-6edd1ebb2c21eb6aca2f", "file": "task-delete.spec.ts", "line": 199, "column": 7}, {"title": "should handle deletion of high priority tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-dc5057367a67b736c2d4", "file": "task-delete.spec.ts", "line": 225, "column": 7}, {"title": "should delete a task successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-1668f805727c7f42c889", "file": "task-delete.spec.ts", "line": 42, "column": 7}, {"title": "should delete task with natural language", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-41f3caf813a16737f0f5", "file": "task-delete.spec.ts", "line": 64, "column": 7}, {"title": "should handle non-existent task deletion gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-796e9bae8794e0268e8b", "file": "task-delete.spec.ts", "line": 80, "column": 7}, {"title": "should enforce tenant isolation during deletion", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-451c18a1c2fb4f1a37b7", "file": "task-delete.spec.ts", "line": 96, "column": 7}, {"title": "should delete multiple tasks in sequence", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-198594e5486b503839b0", "file": "task-delete.spec.ts", "line": 125, "column": 7}, {"title": "should confirm deletion with task details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-5cdb90541b1a1d5b22b4", "file": "task-delete.spec.ts", "line": 177, "column": 7}, {"title": "should handle deletion of completed tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-e919a4a1083fddb60182", "file": "task-delete.spec.ts", "line": 199, "column": 7}, {"title": "should handle deletion of high priority tasks", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "315347c8d367eff27105-78838eb0bf40d17a73a9", "file": "task-delete.spec.ts", "line": 225, "column": 7}]}]}, {"title": "task-read.spec.ts", "file": "task-read.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Task Reading", "file": "task-read.spec.ts", "line": 16, "column": 6, "specs": [{"title": "should list all tasks for user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-54c2155c7b067816be9e", "file": "task-read.spec.ts", "line": 66, "column": 7}, {"title": "should read tasks with natural language query", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-5b48b650dcf8c8dca094", "file": "task-read.spec.ts", "line": 90, "column": 7}, {"title": "should filter tasks by status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-5038dc23f6cd1f458ab8", "file": "task-read.spec.ts", "line": 106, "column": 7}, {"title": "should filter tasks by priority", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-8c2ed11e36c5033071b4", "file": "task-read.spec.ts", "line": 127, "column": 7}, {"title": "should handle empty task list gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-3ba07ae4a09530a032c7", "file": "task-read.spec.ts", "line": 148, "column": 7}, {"title": "should enforce tenant isolation when reading", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-6c2245ad2e9e7ff06b64", "file": "task-read.spec.ts", "line": 169, "column": 7}, {"title": "should read tasks with complex filtering", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-848dc8faaab632b4f8db", "file": "task-read.spec.ts", "line": 190, "column": 7}, {"title": "should list all tasks for user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-b42351b603a0061c97dc", "file": "task-read.spec.ts", "line": 66, "column": 7}, {"title": "should read tasks with natural language query", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-cc2a13b4297547fd8f65", "file": "task-read.spec.ts", "line": 90, "column": 7}, {"title": "should filter tasks by status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-49624d8fa605e1c97187", "file": "task-read.spec.ts", "line": 106, "column": 7}, {"title": "should filter tasks by priority", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-d62be745791a7c39a245", "file": "task-read.spec.ts", "line": 127, "column": 7}, {"title": "should handle empty task list gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-db7bd49b71cec88181b6", "file": "task-read.spec.ts", "line": 148, "column": 7}, {"title": "should enforce tenant isolation when reading", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-43a064502ce078967f6a", "file": "task-read.spec.ts", "line": 169, "column": 7}, {"title": "should read tasks with complex filtering", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-19f52ef9a7c5b3a085f5", "file": "task-read.spec.ts", "line": 190, "column": 7}, {"title": "should list all tasks for user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-8494354c98ea9e0b68b9", "file": "task-read.spec.ts", "line": 66, "column": 7}, {"title": "should read tasks with natural language query", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-574f06028beb795ef6a9", "file": "task-read.spec.ts", "line": 90, "column": 7}, {"title": "should filter tasks by status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-98b1a4a949aacf3c5e66", "file": "task-read.spec.ts", "line": 106, "column": 7}, {"title": "should filter tasks by priority", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-bd1fdbe94bd92ffa8cc2", "file": "task-read.spec.ts", "line": 127, "column": 7}, {"title": "should handle empty task list gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-3171b1f8addfc7e03470", "file": "task-read.spec.ts", "line": 148, "column": 7}, {"title": "should enforce tenant isolation when reading", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-4b964b9ae7761ce67a6e", "file": "task-read.spec.ts", "line": 169, "column": 7}, {"title": "should read tasks with complex filtering", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "bb1ecb6f104c318c0b48-b65dd6e3c7609438effc", "file": "task-read.spec.ts", "line": 190, "column": 7}]}]}, {"title": "task-update.spec.ts", "file": "task-update.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Task Updates", "file": "task-update.spec.ts", "line": 18, "column": 6, "specs": [{"title": "should update task title and description", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-137925d70301e2928ada", "file": "task-update.spec.ts", "line": 43, "column": 7}, {"title": "should update task status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-59d10b17d8162d7a0611", "file": "task-update.spec.ts", "line": 68, "column": 7}, {"title": "should update task priority", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-e25aebf8a71b9b05750b", "file": "task-update.spec.ts", "line": 90, "column": 7}, {"title": "should update task due date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-9f4b9aaed5f68c5ec9c0", "file": "task-update.spec.ts", "line": 112, "column": 7}, {"title": "should complete a task", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-d735474fb4fbf1e676e3", "file": "task-update.spec.ts", "line": 136, "column": 7}, {"title": "should update task with natural language", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-89ca0017903be85d61f9", "file": "task-update.spec.ts", "line": 153, "column": 7}, {"title": "should handle non-existent task update gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-95fb52e7f230d19d3ed0", "file": "task-update.spec.ts", "line": 171, "column": 7}, {"title": "should enforce tenant isolation during updates", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-e7962ef0bb886b744efd", "file": "task-update.spec.ts", "line": 191, "column": 7}, {"title": "should update multiple fields simultaneously", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-5402759445161aca6d79", "file": "task-update.spec.ts", "line": 224, "column": 7}, {"title": "should update task title and description", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-7d34cf1b6aeab18ee2ee", "file": "task-update.spec.ts", "line": 43, "column": 7}, {"title": "should update task status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-ec9c833c8fca3ecaeecf", "file": "task-update.spec.ts", "line": 68, "column": 7}, {"title": "should update task priority", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-45beadda88f5b84e34ff", "file": "task-update.spec.ts", "line": 90, "column": 7}, {"title": "should update task due date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-94b9e5162761e112824a", "file": "task-update.spec.ts", "line": 112, "column": 7}, {"title": "should complete a task", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-d55eec1734ca0de00673", "file": "task-update.spec.ts", "line": 136, "column": 7}, {"title": "should update task with natural language", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-8739d1ee8359e86170df", "file": "task-update.spec.ts", "line": 153, "column": 7}, {"title": "should handle non-existent task update gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-79d925e99d075333b5b9", "file": "task-update.spec.ts", "line": 171, "column": 7}, {"title": "should enforce tenant isolation during updates", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-5635b38178eb7647b718", "file": "task-update.spec.ts", "line": 191, "column": 7}, {"title": "should update multiple fields simultaneously", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-f64e93b2c1f0d11ef490", "file": "task-update.spec.ts", "line": 224, "column": 7}, {"title": "should update task title and description", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-46738ae2dd6b2683819a", "file": "task-update.spec.ts", "line": 43, "column": 7}, {"title": "should update task status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-64deff71ccd6fa1ea55e", "file": "task-update.spec.ts", "line": 68, "column": 7}, {"title": "should update task priority", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-85dcfc619ec9303143f4", "file": "task-update.spec.ts", "line": 90, "column": 7}, {"title": "should update task due date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-6a12b3f40365004056f5", "file": "task-update.spec.ts", "line": 112, "column": 7}, {"title": "should complete a task", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-0f6b9317f4768b37cee0", "file": "task-update.spec.ts", "line": 136, "column": 7}, {"title": "should update task with natural language", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-c1d8cec8df229f221e2d", "file": "task-update.spec.ts", "line": 153, "column": 7}, {"title": "should handle non-existent task update gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-b824116b930fad75e28e", "file": "task-update.spec.ts", "line": 171, "column": 7}, {"title": "should enforce tenant isolation during updates", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-f96484681366dc5b5079", "file": "task-update.spec.ts", "line": 191, "column": 7}, {"title": "should update multiple fields simultaneously", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "8908b03e670bae75aae6-e1d1e95ff3f9d59fcfbf", "file": "task-update.spec.ts", "line": 224, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-30T14:26:56.114Z", "duration": 249.97500000000002, "expected": 0, "skipped": 117, "unexpected": 0, "flaky": 0}}