# Test info

- Name: Task Creation >> should create a basic task successfully
- Location: /mnt/persist/workspace/e2e/task/tests/task-create.spec.ts:30:7

# Error details

```
Error: browserType.launch: 
╔══════════════════════════════════════════════════════╗
║ Host system is missing dependencies to run browsers. ║
║ Please install them with the following command:      ║
║                                                      ║
║     sudo npx playwright install-deps                 ║
║                                                      ║
║ Alternatively, use apt:                              ║
║     sudo apt-get install libnss3\                    ║
║         libnspr4\                                    ║
║         libatk1.0-0\                                 ║
║         libatk-bridge2.0-0\                          ║
║         libatspi2.0-0\                               ║
║         libxcomposite1\                              ║
║         libxdamage1\                                 ║
║         libxfixes3\                                  ║
║         libxrandr2\                                  ║
║         libgbm1\                                     ║
║         libxkbcommon0\                               ║
║         libasound2                                   ║
║                                                      ║
║ <3 Playwright Team                                   ║
╚══════════════════════════════════════════════════════╝
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import { TEST_USERS } from '../utils/auth';
   3 | import { 
   4 |   invokeTaskAgent, 
   5 |   createTestTaskData, 
   6 |   assertTaskOperationSuccess,
   7 |   assertTaskOperationError 
   8 | } from '../utils/api';
   9 | import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';
   10 |
   11 | /**
   12 |  * E2E tests for Task Creation via /copilotkit endpoint
   13 |  * 
   14 |  * These tests verify that the task CRUD agent can successfully
   15 |  * create tasks through the LangGraph workflow.
   16 |  */
   17 |
   18 | test.describe('Task Creation', () => {
   19 |   let dbMocks: DatabaseMocks;
   20 |
   21 |   test.beforeEach(async ({ page }) => {
   22 |     dbMocks = new DatabaseMocks();
   23 |     await setupDatabaseMocks(page, dbMocks);
   24 |   });
   25 |
   26 |   test.afterEach(async () => {
   27 |     dbMocks.clear();
   28 |   });
   29 |
>  30 |   test('should create a basic task successfully', async ({ request }) => {
      |       ^ Error: browserType.launch: 
   31 |     const user = TEST_USERS.regularUser;
   32 |     const taskData = createTestTaskData({
   33 |       title: 'Review contract documents',
   34 |       description: 'Review the client contract for any legal issues'
   35 |     });
   36 |
   37 |     const response = await invokeTaskAgent(
   38 |       request,
   39 |       user,
   40 |       'create',
   41 |       taskData
   42 |     );
   43 |
   44 |     assertTaskOperationSuccess(response, 'create');
   45 |     
   46 |     // Verify the task was created in our mock database
   47 |     const tasks = dbMocks.getTasksForTenant(user.firm_id);
   48 |     expect(tasks.length).toBeGreaterThan(0);
   49 |     
   50 |     const createdTask = tasks.find(task => task.title === taskData.title);
   51 |     expect(createdTask).toBeDefined();
   52 |     expect(createdTask!.description).toBe(taskData.description);
   53 |     expect(createdTask!.status).toBe('todo');
   54 |   });
   55 |
   56 |   test('should create a task with priority and due date', async ({ request }) => {
   57 |     const user = TEST_USERS.regularUser;
   58 |     const dueDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(); // 3 days from now
   59 |     
   60 |     const taskData = createTestTaskData({
   61 |       title: 'Urgent: File motion',
   62 |       description: 'File motion for summary judgment',
   63 |       priority: 'urgent',
   64 |       due_date: dueDate
   65 |     });
   66 |
   67 |     const response = await invokeTaskAgent(
   68 |       request,
   69 |       user,
   70 |       'create',
   71 |       taskData
   72 |     );
   73 |
   74 |     assertTaskOperationSuccess(response, 'create');
   75 |     
   76 |     // Verify the task was created with correct priority and due date
   77 |     const tasks = dbMocks.getTasksForTenant(user.firm_id);
   78 |     const createdTask = tasks.find(task => task.title === taskData.title);
   79 |     
   80 |     expect(createdTask).toBeDefined();
   81 |     expect(createdTask!.priority).toBe('urgent');
   82 |     expect(createdTask!.due_date).toBe(dueDate);
   83 |   });
   84 |
   85 |   test('should create a task with case assignment', async ({ request }) => {
   86 |     const user = TEST_USERS.regularUser;
   87 |     const taskData = createTestTaskData({
   88 |       title: 'Prepare deposition questions',
   89 |       description: 'Prepare questions for plaintiff deposition',
   90 |       related_case: 'case-123'
   91 |     });
   92 |
   93 |     const response = await invokeTaskAgent(
   94 |       request,
   95 |       user,
   96 |       'create',
   97 |       taskData
   98 |     );
   99 |
  100 |     assertTaskOperationSuccess(response, 'create');
  101 |     
  102 |     // Verify the task was created with case assignment
  103 |     const tasks = dbMocks.getTasksForTenant(user.firm_id);
  104 |     const createdTask = tasks.find(task => task.title === taskData.title);
  105 |     
  106 |     expect(createdTask).toBeDefined();
  107 |     expect(createdTask!.related_case).toBe('case-123');
  108 |   });
  109 |
  110 |   test('should handle missing required fields gracefully', async ({ request }) => {
  111 |     const user = TEST_USERS.regularUser;
  112 |     
  113 |     // Try to create a task without a title
  114 |     const taskData = createTestTaskData({
  115 |       title: '', // Empty title
  116 |       description: 'Task without title'
  117 |     });
  118 |
  119 |     const response = await invokeTaskAgent(
  120 |       request,
  121 |       user,
  122 |       'create',
  123 |       taskData,
  124 |       undefined,
  125 |       { expectSuccess: false }
  126 |     );
  127 |
  128 |     // Should either succeed with a default title or fail gracefully
  129 |     if (response.error) {
  130 |       assertTaskOperationError(response, 'title');
```